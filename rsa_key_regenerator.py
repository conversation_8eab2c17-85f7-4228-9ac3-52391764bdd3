#!/usr/bin/env python3
"""
RSA Key Regenerator
Parses RSA key parameters from brut.txt and regenerates complete RSA keys
"""

import os
import sys
from rsa_tool import regenerate_full_keys, rsa_encrypt, rsa_decrypt, save_full_key_to_file

def parse_brut_file(filename="brut.txt"):
    """
    Parse the brut.txt file to extract RSA key parameters.
    Expected format:
    - Lines after 'pexp' contain the private exponent (d)
    - Lines after 'mod' contain the modulus (n)  
    - Lines after 'e' contain the public exponent (e)
    """
    
    if not os.path.exists(filename):
        raise FileNotFoundError(f"File {filename} not found")
    
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    # Clean lines and remove empty ones
    lines = [line.strip() for line in lines if line.strip()]
    
    # Find the sections
    pexp_start = None
    mod_start = None
    e_start = None
    
    for i, line in enumerate(lines):
        if line == 'pexp':
            pexp_start = i + 1
        elif line == 'mod':
            mod_start = i + 1
        elif line == 'e':
            e_start = i + 1
    
    if pexp_start is None:
        raise ValueError("Could not find 'pexp' section in file")
    if mod_start is None:
        raise ValueError("Could not find 'mod' section in file")
    if e_start is None:
        raise ValueError("Could not find 'e' section in file")
    
    # Extract private exponent (d)
    d_str = ""
    for i in range(pexp_start, len(lines)):
        if lines[i] in ['mod', 'e']:
            break
        d_str += lines[i]
    
    # Extract modulus (n)
    n_str = ""
    for i in range(mod_start, len(lines)):
        if lines[i] in ['pexp', 'e']:
            break
        n_str += lines[i]
    
    # Extract public exponent (e)
    e_str = ""
    for i in range(e_start, len(lines)):
        if lines[i] in ['pexp', 'mod']:
            break
        e_str += lines[i]
    
    # Convert to integers
    try:
        d = int(d_str)
        n = int(n_str)
        e = int(e_str)
    except ValueError as ve:
        raise ValueError(f"Failed to parse integers from file: {ve}")
    
    return n, e, d

def test_keys_with_file(public_key, private_key, test_filename="ciphtest.txt"):
    """
    Test the regenerated keys by encrypting and decrypting a test file.
    """
    if not os.path.exists(test_filename):
        print(f"Warning: Test file {test_filename} not found. Using default test message.")
        test_content = "This is a test message for RSA key verification."
    else:
        with open(test_filename, 'r') as f:
            test_content = f.read().strip()
    
    print(f"Testing with content: '{test_content}'")
    
    try:
        # Encrypt the test content
        print("Encrypting test content...")
        ciphertext = rsa_encrypt(test_content, public_key)
        print(f"Encryption successful. Ciphertext length: {len(ciphertext)} bytes")
        print(f"Ciphertext (first 64 hex chars): {ciphertext.hex()[:64]}...")
        
        # Decrypt the ciphertext
        print("Decrypting ciphertext...")
        decrypted_bytes = rsa_decrypt(ciphertext, private_key)
        decrypted_text = decrypted_bytes.decode('utf-8')
        print(f"Decryption successful. Decrypted: '{decrypted_text}'")
        
        # Verify round-trip
        if test_content == decrypted_text:
            print("✅ Round-trip test PASSED! Keys are working correctly.")
            return True
        else:
            print("❌ Round-trip test FAILED! Decrypted text doesn't match original.")
            print(f"Original:  '{test_content}'")
            print(f"Decrypted: '{decrypted_text}'")
            return False
            
    except Exception as e:
        print(f"❌ Key test failed with error: {e}")
        return False

def main():
    print("RSA Key Regenerator")
    print("=" * 50)
    
    try:
        # Parse the brut.txt file
        print("Parsing brut.txt file...")
        n, e, d = parse_brut_file()
        
        print("Successfully extracted RSA parameters:")
        print(f"Modulus (n):          {n.bit_length()} bits")
        print(f"Public exponent (e):  {e}")
        print(f"Private exponent (d): {d.bit_length()} bits")
        print()
        
        # Regenerate full keys
        print("Regenerating complete RSA key parameters...")
        print("This may take a moment for large keys...")
        
        full_keys = regenerate_full_keys(n, e, d)
        
        print("✅ Key regeneration successful!")
        print()
        
        # Display complete key information
        print("COMPLETE RSA KEY PARAMETERS")
        print("=" * 50)
        
        pub = full_keys['public']
        priv = full_keys['private']
        
        print(f"Modulus (n):          {hex(pub['n'])}")
        print(f"Public exponent (e):  {pub['e']}")
        print(f"Private exponent (d): {hex(priv['d'])}")
        print(f"Prime p:              {hex(priv['p'])}")
        print(f"Prime q:              {hex(priv['q'])}")
        print(f"Euler's totient φ(n): {hex(priv['phi'])}")
        print(f"CRT exponent dp:      {hex(priv['dp'])}")
        print(f"CRT exponent dq:      {hex(priv['dq'])}")
        print(f"CRT coefficient qinv: {hex(priv['qinv'])}")
        print()
        
        # Verify key parameters
        print("Key verification:")
        p_times_q_equals_n = priv['p'] * priv['q'] == pub['n']
        ed_mod_phi_equals_1 = (pub['e'] * priv['d']) % priv['phi'] == 1
        
        print(f"p × q = n: {'✅' if p_times_q_equals_n else '❌'}")
        print(f"ed ≡ 1 (mod φ): {'✅' if ed_mod_phi_equals_1 else '❌'}")
        print(f"Key size: {pub['n'].bit_length()} bits")
        print()
        
        if not (p_times_q_equals_n and ed_mod_phi_equals_1):
            print("❌ Key verification failed! There may be an issue with the extracted parameters.")
            return
        
        # Save the regenerated keys
        print("Saving regenerated keys...")
        save_full_key_to_file(full_keys['public'], 'regenerated_public.key')
        save_full_key_to_file(full_keys['private'], 'regenerated_private.key')
        print("Keys saved to regenerated_public.key and regenerated_private.key")
        print()
        
        # Test the keys with ciphtest.txt
        print("Testing regenerated keys with ciphtest.txt...")
        print("-" * 50)
        test_success = test_keys_with_file(full_keys['public'], full_keys['private'])
        
        if test_success:
            print("\n🎉 SUCCESS! RSA keys have been successfully regenerated and tested.")
        else:
            print("\n⚠️  Keys were regenerated but testing failed. Please check the implementation.")
            
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        print("Make sure brut.txt exists in the current directory.")
    except ValueError as e:
        print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
