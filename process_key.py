from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
import base64

def parse_asn1_manually(der_bytes):
    """Parse ASN.1 structure manually to extract RSA parameters"""
    
    def read_integer(data, offset):
        """Read an ASN.1 INTEGER from the data"""
        if data[offset] != 0x02:  # INTEGER tag
            raise ValueError(f"Expected INTEGER tag (0x02), got {hex(data[offset])}")
        
        offset += 1
        length_byte = data[offset]
        offset += 1
        
        if length_byte & 0x80:  # Long form length
            length_bytes = length_byte & 0x7f
            length = 0
            for i in range(length_bytes):
                length = (length << 8) | data[offset]
                offset += 1
        else:  # Short form length
            length = length_byte
        
        # Read the integer value
        integer_bytes = data[offset:offset + length]
        integer_value = int.from_bytes(integer_bytes, 'big')
        
        return integer_value, offset + length
    
    print("Parsing ASN.1 structure...")
    print(f"Total DER length: {len(der_bytes)} bytes")
    
    # Skip the outer SEQUENCE header
    offset = 0
    if der_bytes[0] == 0x30:  # SEQUENCE tag
        offset = 1
        length_byte = der_bytes[offset]
        offset += 1
        
        if length_byte & 0x80:  # Long form
            length_bytes = length_byte & 0x7f
            for i in range(length_bytes):
                offset += 1
    
    print(f"Starting to parse integers at offset: {offset}")
    
    try:
        # Parse RSA parameters in order: version, n, e, d, p, q, dp, dq, qinv
        parameters = []
        param_names = ['version', 'n', 'e', 'd', 'p', 'q', 'dp', 'dq', 'qinv']
        
        for i, name in enumerate(param_names):
            if offset >= len(der_bytes):
                print(f"Reached end of data at parameter {i} ({name})")
                break
                
            try:
                value, offset = read_integer(der_bytes, offset)
                parameters.append((name, value))
                if name in ['version', 'e']:
                    print(f"{name}: {value}")
                else:
                    print(f"{name}: {hex(value)[:50]}...")
                
                if name == 'n':
                    n_bits = value.bit_length()
                    print(f"  Modulus bit length: {n_bits}")
                    
            except Exception as e:
                print(f"Error parsing {name}: {e}")
                print(f"Remaining bytes: {len(der_bytes) - offset}")
                break
        
        return parameters
        
    except Exception as e:
        print(f"Error in manual parsing: {e}")
        return []

def reconstruct_full_key(n, e, d, p, q, dp, dq):
    """Reconstruct the complete RSA private key"""
    
    # Calculate missing parameter
    qinv = pow(q, -1, p)
    
    print(f"\nReconstructed RSA key parameters:")
    print(f"n = {hex(n)[:50]}...")
    print(f"e = {e}")
    print(f"d = {hex(d)[:50]}...")
    print(f"p = {hex(p)[:50]}...")
    print(f"q = {hex(q)[:50]}...")
    print(f"dp = {hex(dp)[:50]}...")
    print(f"dq = {hex(dq)[:50]}...")
    print(f"qinv = {hex(qinv)[:50]}...")
    
    # Verify the parameters
    print(f"\nVerifying parameters:")
    print(f"p * q == n: {p * q == n}")
    print(f"d * e == 1 (mod phi): {(d * e) % ((p-1)*(q-1)) == 1}")
    print(f"dp == d mod (p-1): {dp == d % (p-1)}")
    print(f"dq == d mod (q-1): {dq == d % (q-1)}")
    
    # Create PEM format
    try:
        # Create RSA key object
        private_key = rsa.RSAPrivateNumbers(
            p=p, q=q, d=d, dmp1=dp, dmq1=dq, iqmp=qinv,
            public_numbers=rsa.RSAPublicNumbers(e=e, n=n)
        ).private_key()
        
        # Export as traditional RSA private key format
        pem_data = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.TraditionalOpenSSL,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        print(f"\nComplete PEM key:")
        print(pem_data.decode())
        
        # Save to file
        with open('recovered_key.pem', 'wb') as f:
            f.write(pem_data)
        print("Saved complete key to 'recovered_key.pem'")
        
        return True
        
    except Exception as e:
        print(f"Error creating PEM: {e}")
        return False

def parse_rsa_key():
    # Read the PEM file
    with open('requirements.txt', 'r') as f:
        pem_data = f.read()
    
    try:
        # Load the private key (even if incomplete)
        private_key = serialization.load_pem_private_key(
            pem_data.encode(),
            password=None
        )
        
        # Extract the numbers
        private_numbers = private_key.private_numbers()
        public_numbers = private_numbers.public_numbers
        
        print("RSA Parameters extracted:")
        print(f"n (modulus): {hex(public_numbers.n)}")
        print(f"e (public exponent): {public_numbers.e}")
        print(f"d (private exponent): {hex(private_numbers.d)}")
        print(f"p (prime 1): {hex(private_numbers.p)}")
        print(f"q (prime 2): {hex(private_numbers.q)}")
        print(f"dp (d mod p-1): {hex(private_numbers.dmp1)}")
        print(f"dq (d mod q-1): {hex(private_numbers.dmq1)}")
        print(f"qinv (q^-1 mod p): {hex(private_numbers.iqmp)}")
        
    except Exception as e:
        print(f"Error loading key: {e}")
        print("Key might be truncated - trying manual base64 decode...")
        
        # Manual base64 decode
        lines = pem_data.strip().split('\n')
        base64_data = ''.join(lines[1:-1])  # Remove BEGIN/END lines
        
        try:
            der_bytes = base64.b64decode(base64_data)
            print(f"DER data length: {len(der_bytes)} bytes")
            
            # Parse ASN.1 manually
            parameters = parse_asn1_manually(der_bytes)
            
            # Extract parameters and reconstruct
            if parameters:
                param_dict = dict(parameters)
                
                # Check if we have all the necessary parameters
                required = ['n', 'e', 'd', 'p', 'q', 'dp', 'dq']
                if all(param in param_dict for param in required):
                    print("\nAll required parameters found!")
                    
                    success = reconstruct_full_key(
                        param_dict['n'], param_dict['e'], param_dict['d'],
                        param_dict['p'], param_dict['q'], 
                        param_dict['dp'], param_dict['dq']
                    )
                    
                    if success:
                        print("\n✅ Successfully recovered the complete RSA private key!")
                        print("The key has been saved as 'recovered_key.pem'")
                else:
                    missing = [p for p in required if p not in param_dict]
                    print(f"Missing parameters: {missing}")
            
            return der_bytes
        except Exception as e2:
            print(f"Base64 decode error: {e2}")

if __name__ == "__main__":
    parse_rsa_key()
