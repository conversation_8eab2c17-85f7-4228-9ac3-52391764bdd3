#!/usr/bin/env python3
"""
True Character-by-Character Interactive OCR with Visual Feedback
Forces individual character detection and saves each as char.png
"""

import os
import sys
import json
import argparse
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageDraw, ImageFont
import pytesseract
import logging

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# POSITIONING CONFIGURATION CONSTANTS
# ============================================================================
# Adjust these values to fine-tune character positioning and display

# Character Width and Spacing
CHAR_WIDTH_RATIO = 0.32         # Portion of line width for character spacing (0.75 = 75%)
CHAR_HEIGHT_RATIO = 0.8          # Portion of line height for character height (0.8 = 80%)
FALLBACK_CHAR_WIDTH_RATIO = 0.5  # Fallback width ratio when no line text available

# Starting Position and Margins
LEFT_MARGIN_RATIO = 0.015        # Left margin as portion of image width (0.005 = 0.5%)
ADDITIONAL_LEFT_SHIFT = 8        # Additional pixels to shift left from calculated position
LINE_HEIGHT_RATIO = 0.85         # Line height multiplier to prevent going too far down

# Context Display Settings
CONTEXT_CHARS = 5                # Number of neighboring characters on each side
CONTEXT_PADDING = 5              # Padding around context region in pixels
CONTEXT_LEFT_SHIFT = 8           # Left shift for context extraction

# Image Scaling and Size Limits
CONTEXT_SCALE = 6                # Scale factor for context region display
MAX_CONTEXT_WIDTH_RATIO = 0.1    # Max context width as portion of image width
MAX_CONTEXT_HEIGHT = 60          # Max context height in pixels
MIN_CONTEXT_WIDTH = 50           # Minimum context width in pixels
MIN_CONTEXT_HEIGHT = 20          # Minimum context height in pixels

# Final Image Dimensions
MAX_SCALED_WIDTH = 800           # Maximum width of scaled context image
MAX_SCALED_HEIGHT = 200          # Maximum height of scaled context image
MIN_SCALED_WIDTH = 200           # Minimum width of scaled context image
MIN_SCALED_HEIGHT = 60           # Minimum height of scaled context image

# Visual Highlighting
HIGHLIGHT_BORDER_WIDTH = 4       # Width of red border around current character
HIGHLIGHT_ACCENT_WIDTH = 2       # Width of yellow accent border
INFO_HEIGHT = 160                # Height reserved for character information text

# ============================================================================

class TrueCharacterOCR:
    """True character-by-character OCR with visual feedback"""
    
    def __init__(self):
        self.context_window = 3
    
    def extract_text_as_string(self, image_path: str) -> str:
        """Extract text as a single string using optimal settings"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        # Get text using optimal configuration
        text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1")
        
        return text.strip()
    
    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimal preprocessing based on previous findings"""
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array.copy()
        
        # Apply 3x scaling with Lanczos interpolation
        height, width = gray.shape
        new_width = int(width * 3.0)
        new_height = int(height * 3.0)
        scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply manual threshold at 180
        _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
        
        return thresh
    
    def extract_character_positions(self, image_path: str) -> List[Dict]:
        """Extract individual character positions using advanced OCR analysis"""
        try:
            # Load and preprocess image
            img = Image.open(image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Apply optimal preprocessing
            img_array = np.array(img)
            processed_img = self._preprocess_image(img_array)
            pil_img = Image.fromarray(processed_img)

            # Get word-level data first
            data = pytesseract.image_to_data(pil_img, config="--psm 6 --oem 1", output_type=pytesseract.Output.DICT)

            # Extract text to get character sequence
            full_text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1").strip()

            characters = []
            char_index = 0

            # Process each word and estimate character positions within words
            for i in range(len(data['text'])):
                word_text = data['text'][i]
                conf = int(data['conf'][i]) if data['conf'][i] != '-1' else 0

                if word_text.strip() and conf > 0:  # Only process confident, non-empty words
                    word_bbox = {
                        'left': data['left'][i],
                        'top': data['top'][i],
                        'width': data['width'][i],
                        'height': data['height'][i]
                    }

                    # Estimate character positions within this word
                    word_chars = list(word_text)
                    char_width = word_bbox['width'] / len(word_chars) if word_chars else word_bbox['width']

                    for j, char in enumerate(word_chars):
                        char_left = word_bbox['left'] + int(j * char_width)
                        char_bbox = {
                            'left': char_left,
                            'top': word_bbox['top'],
                            'width': int(char_width),
                            'height': word_bbox['height']
                        }

                        characters.append({
                            'char': char,
                            'confidence': conf,
                            'bbox': char_bbox,
                            'line_num': data['line_num'][i],
                            'word_num': data['word_num'][i]
                        })
                        char_index += 1

                # Handle spaces between words
                if i < len(data['text']) - 1:
                    next_word = data['text'][i + 1]
                    if next_word.strip() and data['line_num'][i] == data['line_num'][i + 1]:
                        # Add space character between words on same line
                        space_left = word_bbox['left'] + word_bbox['width']
                        next_left = data['left'][i + 1] if i + 1 < len(data['left']) else space_left + 10
                        space_width = max(10, next_left - space_left)

                        space_bbox = {
                            'left': space_left,
                            'top': word_bbox['top'],
                            'width': space_width,
                            'height': word_bbox['height']
                        }

                        characters.append({
                            'char': ' ',
                            'confidence': conf,
                            'bbox': space_bbox,
                            'line_num': data['line_num'][i],
                            'word_num': data['word_num'][i]
                        })
                        char_index += 1

            return characters

        except Exception as e:
            logger.error(f"Failed to extract character positions: {e}")
            return []

    def estimate_character_position(self, image_path: str, text: str, char_index: int) -> dict:
        """Estimate character position with improved accuracy for monospace fonts"""
        try:
            # Load image to get dimensions
            img = Image.open(image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Split text into lines
            lines = text.split('\n')

            # Find which line and position within line
            current_pos = 0
            line_num = 0
            char_in_line = char_index
            current_line_text = ""

            for i, line in enumerate(lines):
                line_length = len(line)
                if current_pos + line_length >= char_index:
                    line_num = i
                    char_in_line = char_index - current_pos
                    current_line_text = line
                    break
                current_pos += line_length + 1  # +1 for newline

            # For monospace fonts (like in RSA keys), use more precise estimation
            # Typical monospace character width is about 60% of height
            estimated_line_height = img.height / max(len(lines), 1)

            # For RSA keys, characters are typically tightly packed
            # Estimate based on actual line content using configurable constants
            if current_line_text:
                estimated_char_width = img.width * CHAR_WIDTH_RATIO / len(current_line_text)
            else:
                estimated_char_width = estimated_line_height * FALLBACK_CHAR_WIDTH_RATIO

            # Start close to left edge using configurable margin
            line_start_margin = img.width * LEFT_MARGIN_RATIO

            # Calculate position with configurable left shift adjustment
            estimated_x = int(line_start_margin + char_in_line * estimated_char_width)
            # Apply additional left shift to compensate for starting too far right
            estimated_x = max(0, estimated_x - ADDITIONAL_LEFT_SHIFT)
            # Reduce line height multiplier to prevent going too far down
            estimated_y = int(line_num * estimated_line_height * LINE_HEIGHT_RATIO)

            # Ensure character width is reasonable
            char_width = max(int(estimated_char_width), 8)
            char_height = max(int(estimated_line_height * CHAR_HEIGHT_RATIO), 12)

            return {
                'left': max(0, estimated_x),
                'top': max(0, estimated_y),
                'width': char_width,
                'height': char_height
            }

        except Exception as e:
            logger.error(f"Failed to estimate character position: {e}")
            return {
                'left': 0,
                'top': 0,
                'width': 20,
                'height': 20
            }

    def create_character_image(self, image_path: str, text: str, char_index: int, char_filename: str = "char.png"):
        """Create a focused single character visualization"""
        try:
            if char_index >= len(text):
                return

            char = text[char_index]

            # Load original image
            img = Image.open(image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Get character position for the current character
            char_bbox = self.estimate_character_position(image_path, text, char_index)

            # Calculate positions for neighboring characters using configurable constants
            start_char_index = max(0, char_index - CONTEXT_CHARS)
            end_char_index = min(len(text) - 1, char_index + CONTEXT_CHARS)

            # Get bounding boxes for the context range
            start_bbox = self.estimate_character_position(image_path, text, start_char_index)
            end_bbox = self.estimate_character_position(image_path, text, end_char_index)

            # Calculate the wider region that includes neighboring characters
            # Determine the extraction region
            context_left = max(0, start_bbox['left'] - CONTEXT_PADDING - CONTEXT_LEFT_SHIFT)
            context_top = max(0, char_bbox['top'] - CONTEXT_PADDING)
            context_right = min(img.width, end_bbox['left'] + end_bbox['width'] + CONTEXT_PADDING - CONTEXT_LEFT_SHIFT)
            context_bottom = min(img.height, char_bbox['top'] + char_bbox['height'] + CONTEXT_PADDING)

            # Ensure reasonable region size using configurable limits
            max_context_width = int(img.width * MAX_CONTEXT_WIDTH_RATIO)
            max_context_height = min(MAX_CONTEXT_HEIGHT, img.height // 8)

            if context_right - context_left > max_context_width:
                # Center the region around the current character
                char_center_x = char_bbox['left'] + char_bbox['width'] // 2
                context_left = max(0, char_center_x - max_context_width // 2)
                context_right = min(img.width, context_left + max_context_width)

            if context_bottom - context_top > max_context_height:
                context_bottom = context_top + max_context_height

            left, top, right, bottom = context_left, context_top, context_right, context_bottom

            # Ensure minimum reasonable size for the context region using constants
            if right - left < MIN_CONTEXT_WIDTH:
                center_x = (left + right) // 2
                left = max(0, center_x - MIN_CONTEXT_WIDTH // 2)
                right = min(img.width, center_x + MIN_CONTEXT_WIDTH // 2)

            if bottom - top < MIN_CONTEXT_HEIGHT:
                center_y = (top + bottom) // 2
                top = max(0, center_y - MIN_CONTEXT_HEIGHT // 2)
                bottom = min(img.height, center_y + MIN_CONTEXT_HEIGHT // 2)

            # Extract the wider context region
            context_region = img.crop((left, top, right, bottom))

            # Apply preprocessing to the context region for better visibility
            context_array = np.array(context_region)
            if len(context_array.shape) == 3:
                context_gray = cv2.cvtColor(context_array, cv2.COLOR_RGB2GRAY)
            else:
                context_gray = context_array.copy()

            # Apply threshold to make characters clearer
            _, context_thresh = cv2.threshold(context_gray, 180, 255, cv2.THRESH_BINARY)
            context_region = Image.fromarray(context_thresh)

            # Scale up moderately for better visibility of the context using constants
            scale = CONTEXT_SCALE
            new_width = max(context_region.width * scale, MIN_SCALED_WIDTH)
            new_height = max(context_region.height * scale, MIN_SCALED_HEIGHT)

            # Cap maximum scaled size to prevent huge images using constants
            if new_width > MAX_SCALED_WIDTH:
                scale_factor = MAX_SCALED_WIDTH / new_width
                new_width = MAX_SCALED_WIDTH
                new_height = int(new_height * scale_factor)
            if new_height > MAX_SCALED_HEIGHT:
                scale_factor = MAX_SCALED_HEIGHT / new_height
                new_height = MAX_SCALED_HEIGHT
                new_width = int(new_width * scale_factor)

            context_region = context_region.resize((new_width, new_height), Image.NEAREST)

            # Create final image with character info using constants
            final_width = max(new_width + 60, 600)  # Wider for context display
            final_height = new_height + INFO_HEIGHT + 60

            final_img = Image.new('RGB', (final_width, final_height), 'white')

            # Center the context region
            context_x = (final_width - new_width) // 2
            context_y = 30
            final_img.paste(context_region, (context_x, context_y))

            # Calculate the position of the current character within the context region
            # Character position relative to the extracted region
            char_rel_x = char_bbox['left'] - left
            char_rel_y = char_bbox['top'] - top

            # Scale the relative position
            scaled_char_x = int(char_rel_x * scale)
            scaled_char_y = int(char_rel_y * scale)

            # Character dimensions scaled
            scaled_char_width = int(char_bbox['width'] * scale)
            scaled_char_height = int(char_bbox['height'] * scale)

            # Add highlighting around the current character using constants
            draw = ImageDraw.Draw(final_img)
            highlight_x = context_x + scaled_char_x
            highlight_y = context_y + scaled_char_y

            # Draw red border around the current character
            border_offset = HIGHLIGHT_BORDER_WIDTH // 2
            draw.rectangle([highlight_x-border_offset-1, highlight_y-border_offset-1,
                          highlight_x+scaled_char_width+border_offset, highlight_y+scaled_char_height+border_offset],
                         outline='red', width=HIGHLIGHT_BORDER_WIDTH)

            # Add a subtle background highlight
            accent_offset = HIGHLIGHT_ACCENT_WIDTH // 2
            draw.rectangle([highlight_x-accent_offset, highlight_y-accent_offset,
                          highlight_x+scaled_char_width+accent_offset-1, highlight_y+scaled_char_height+accent_offset-1],
                         outline='yellow', width=HIGHLIGHT_ACCENT_WIDTH)

            # Add title
            try:
                font = ImageFont.load_default()
            except:
                font = None

            draw.text((20, 5), f"CHARACTER VALIDATION WITH CONTEXT", fill='darkblue', font=font)

            # Character info section
            info_y = context_y + new_height + 20

            # Main character info
            char_display = repr(char) if char in ['\n', '\t', ' '] else f"'{char}'"
            draw.text((20, info_y), f"Character: {char_display}", fill='black', font=font)
            draw.text((20, info_y + 20), f"Position: {char_index + 1} of {len(text)}", fill='black', font=font)

            # ASCII and type info
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Space" if char == ' ' else "Newline" if char == '\n' else "Symbol"
            draw.text((20, info_y + 40), f"ASCII: {ord(char)} (0x{ord(char):02x})", fill='blue', font=font)
            draw.text((20, info_y + 60), f"Type: {char_type}", fill='blue', font=font)

            # Position info
            draw.text((20, info_y + 80), f"Image Region: ({left}, {top}) to ({right}, {bottom})", fill='green', font=font)
            draw.text((20, info_y + 100), f"Size: {right-left}x{bottom-top} → {new_width}x{new_height} ({scale}x scale)", fill='green', font=font)

            # Extended context using configurable constant
            start = max(0, char_index - CONTEXT_CHARS)
            end = min(len(text), char_index + CONTEXT_CHARS + 1)
            context = text[start:end]
            before = context[:char_index - start]
            after = context[char_index - start + 1:]
            context_display = f"{before}[{char_display}]{after}"
            draw.text((20, info_y + 120), f"Context (±{CONTEXT_CHARS} chars): {context_display}", fill='purple', font=font)

            # Visual context info
            draw.text((20, info_y + 140), f"Red border highlights current character in context", fill='red', font=font)

            # Save the image
            final_img.save(char_filename)
            logger.debug(f"Created single character image: {char_filename} for '{char}' at position {char_index}")

        except Exception as e:
            logger.error(f"Failed to create character image: {e}")
            # Fallback to simple text visualization
            self._create_fallback_image(text, char_index, char_filename)

    def _create_fallback_image(self, text: str, char_index: int, char_filename: str):
        """Create a simple fallback visualization for single character"""
        try:
            char = text[char_index]
            img = Image.new('RGB', (400, 200), 'white')
            draw = ImageDraw.Draw(img)

            try:
                font = ImageFont.load_default()
            except:
                font = None

            # Title
            draw.text((20, 10), "SINGLE CHARACTER (Fallback Mode)", fill='darkred', font=font)

            # Character display
            char_display = repr(char) if char in ['\n', '\t', ' '] else f"'{char}'"
            draw.text((50, 50), f"Character: {char_display}", fill='red', font=font)
            draw.text((50, 80), f"Position: {char_index + 1} of {len(text)}", fill='blue', font=font)
            draw.text((50, 110), f"ASCII: {ord(char)} (0x{ord(char):02x})", fill='blue', font=font)

            # Type info
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Space" if char == ' ' else "Newline" if char == '\n' else "Symbol"
            draw.text((50, 140), f"Type: {char_type}", fill='green', font=font)
            draw.text((50, 170), "⚠️ Image extraction failed - using text mode", fill='gray', font=font)

            img.save(char_filename)
            logger.info(f"Created fallback character image for '{char}' at position {char_index}")
        except Exception as e:
            logger.error(f"Failed to create fallback image: {e}")
    
    def character_by_character_validation(self, image_path: str, output_file: str = None, max_lines: int = 4) -> str:
        """Perform true character-by-character validation"""
        print("🔍 Starting True Character-by-Character OCR Validation")
        print("=" * 60)
        
        # Extract text as string
        full_text = self.extract_text_as_string(image_path)
        
        if not full_text:
            print("❌ No text detected in image!")
            return ""
        
        # Split into lines and limit to max_lines
        lines = full_text.split('\n')
        if max_lines > 0:
            lines = lines[:max_lines]
        
        # Rejoin limited lines
        text_to_validate = '\n'.join(lines)
        
        print(f"📝 Text to validate ({len(text_to_validate)} characters):")
        print(f"Preview: {repr(text_to_validate[:100])}...")
        print()
        print("💡 Instructions:")
        print("  - Press ENTER to accept the character")
        print("  - Type a different character to correct it")
        print("  - Type 'skip' to skip this character")
        print("  - Type 'quit' to finish early")
        print("  - Type 'auto' to auto-accept remaining characters")
        print("  - Check char.png for visual feedback of current character")
        print()
        
        validated_chars = []
        corrections_made = 0
        auto_mode = False
        
        for i, char in enumerate(text_to_validate):
            # Create character visualization
            self.create_character_image(image_path, text_to_validate, i, "char.png")
            
            # Auto-accept in auto mode (except for problematic characters)
            if auto_mode and char.isalnum():
                validated_chars.append(char)
                continue
            
            # Display character info
            print(f"Character {i+1}/{len(text_to_validate)}")
            print(f"Current character: '{char}' (ASCII: {ord(char)})")
            
            # Show context
            start = max(0, i - 10)
            end = min(len(text_to_validate), i + 11)
            context = text_to_validate[start:end]
            before = context[:i - start]
            after = context[i - start + 1:]
            print(f"Context: {before}[{char}]{after}")
            
            # Show progress
            progress = ''.join(validated_chars) + f"[{char}]"
            remaining = text_to_validate[i+1:i+11]
            print(f"Progress: ...{progress[-20:]}...{remaining[:10]}...")
            print(f"📷 Character visualization saved as: char.png")
            
            # Character type info
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol/Space"
            print(f"Type: {char_type}")
            
            # Get user input
            while True:
                user_input = input(f"Correct character ['{char}']: ").strip()
                
                if user_input == "":
                    # Accept the character
                    validated_chars.append(char)
                    break
                elif user_input.lower() == "skip":
                    # Skip this character
                    print("⏭️  Skipped character")
                    break
                elif user_input.lower() == "quit":
                    # Finish early
                    print("🛑 Validation stopped by user")
                    return ''.join(validated_chars)
                elif user_input.lower() == "auto":
                    # Enable auto mode
                    auto_mode = True
                    validated_chars.append(char)
                    print("🤖 Auto mode enabled for alphanumeric characters")
                    break
                elif len(user_input) == 1:
                    # User provided a correction
                    validated_chars.append(user_input)
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '{user_input}'")
                    break
                elif user_input.lower() == "newline" or user_input.lower() == "\\n":
                    # Special case for newline
                    validated_chars.append('\n')
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '\\n'")
                    break
                else:
                    print("❌ Please enter a single character, 'skip', 'quit', 'auto', or 'newline'")
            
            print()  # Empty line for readability
        
        # Final results
        final_text = ''.join(validated_chars)
        print("=" * 60)
        print("✅ True Character-by-Character Validation Complete!")
        print(f"📊 Characters processed: {len(text_to_validate)}")
        print(f"🔧 Corrections made: {corrections_made}")
        
        # Save validated text if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(final_text)
            print(f"💾 Validated text saved to: {output_file}")
        
        # Clean up char.png
        try:
            os.remove("char.png")
            print("🧹 Cleaned up char.png")
        except:
            pass
        
        return final_text

def load_reference_text(reference_file: str, num_lines: int = 4) -> str:
    """Load reference text for comparison"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        reference_lines = lines[:num_lines]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first {num_lines} lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts"""
    from difflib import SequenceMatcher
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def main():
    parser = argparse.ArgumentParser(description="True Character-by-Character Interactive OCR")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file for validated text")
    parser.add_argument("--reference", "-r", help="Reference file for comparison")
    parser.add_argument("--lines", "-l", type=int, default=4,
                       help="Number of lines to process (default: 4)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    # Initialize true character-by-character OCR
    char_ocr = TrueCharacterOCR()
    
    # Perform character-by-character validation
    validated_text = char_ocr.character_by_character_validation(
        args.image_path, 
        args.output, 
        args.lines
    )
    
    if validated_text:
        print("\n📄 Final validated text:")
        print("=" * 50)
        print(repr(validated_text))  # Use repr to show special characters
        print()
        print("Formatted text:")
        print(validated_text)
        
        # Compare with reference if provided
        if args.reference and os.path.exists(args.reference):
            reference_text = load_reference_text(args.reference, args.lines)
            if reference_text:
                similarity = calculate_similarity(validated_text, reference_text)
                print(f"\n📊 Similarity with reference: {similarity:.1%}")
                
                if similarity >= 0.95:
                    print("🎯 Excellent! 95%+ similarity achieved!")
                elif similarity >= 0.90:
                    print("👍 Good! 90%+ similarity achieved!")
                else:
                    print("📈 Room for improvement. Consider more corrections.")
                
                # Show character-by-character differences
                if similarity < 1.0:
                    print("\nCharacter differences:")
                    for i, (ref_char, val_char) in enumerate(zip(reference_text, validated_text)):
                        if ref_char != val_char:
                            print(f"  Position {i}: '{val_char}' should be '{ref_char}'")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
