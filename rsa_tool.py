#!/usr/bin/env python3
import base64
import os
import secrets
import hashlib

def gcd(a, b):
    """Calculate Greatest Common Divisor."""
    while b:
        a, b = b, a % b
    return a

def mod_inverse(a, m):
    """Calculate modular inverse using Extended Euclidean Algorithm."""
    if gcd(a, m) != 1:
        raise ValueError("Modular inverse does not exist")
    
    def extended_gcd(a, b):
        if a == 0:
            return b, 0, 1
        gcd, x1, y1 = extended_gcd(b % a, a)
        x = y1 - (b // a) * x1
        y = x1
        return gcd, x, y
    
    _, x, _ = extended_gcd(a % m, m)
    return (x % m + m) % m

def is_prime(n, k=5):
    """Miller-Rabin primality test."""
    if n < 2:
        return False
    if n == 2 or n == 3:
        return True
    if n % 2 == 0:
        return False
    
    # Write n-1 as d * 2^r
    d = n - 1
    r = 0
    while d % 2 == 0:
        d //= 2
        r += 1
    
    # Perform k rounds of testing
    for _ in range(k):
        a = secrets.randbelow(n - 3) + 2
        x = pow(a, d, n)
        
        if x == 1 or x == n - 1:
            continue
        
        for _ in range(r - 1):
            x = pow(x, 2, n)
            if x == n - 1:
                break
        else:
            return False
    
    return True

def generate_prime(bits):
    """Generate a random prime number with specified bit length."""
    while True:
        n = secrets.randbits(bits)
        n |= (1 << (bits - 1)) | 1  # Set MSB and LSB
        if is_prime(n):
            return n

def generate_rsa_keypair(bits=1024):
    """Generate RSA key pair."""
    print(f"Generating {bits}-bit RSA key pair...")
    
    # Generate two distinct primes
    p = generate_prime(bits // 2)
    q = generate_prime(bits // 2)
    while p == q:
        q = generate_prime(bits // 2)
    
    n = p * q
    phi = (p - 1) * (q - 1)
    
    # Choose e (commonly 65537)
    e = 65537
    while gcd(e, phi) != 1:
        e += 2
    
    # Calculate d
    d = mod_inverse(e, phi)
    
    return {
        'public': {'n': n, 'e': e},
        'private': {'n': n, 'd': d, 'p': p, 'q': q, 'phi': phi}
    }

def pkcs1_pad(data, key_size):
    """Apply PKCS#1 v1.5 padding for encryption."""
    max_data_len = key_size - 11  # 11 bytes for padding overhead
    if len(data) > max_data_len:
        raise ValueError(f"Data too long for key size. Max: {max_data_len} bytes")
    
    padding_len = key_size - len(data) - 3
    padding = secrets.token_bytes(padding_len)
    # Ensure no zero bytes in padding
    padding = bytes(b if b != 0 else 1 for b in padding)
    
    return b'\x00\x02' + padding + b'\x00' + data

def pkcs1_unpad(data):
    """Remove PKCS#1 v1.5 padding from decrypted data."""
    if len(data) < 11:
        raise ValueError("Invalid padded data")
    
    if data[0:2] != b'\x00\x02':
        raise ValueError("Invalid padding format")
    
    # Find separator
    separator_index = data.find(b'\x00', 2)
    if separator_index == -1:
        raise ValueError("No separator found in padding")
    
    return data[separator_index + 1:]

def rsa_encrypt(plaintext, public_key):
    """Encrypt data using RSA public key."""
    n, e = public_key['n'], public_key['e']
    key_size = (n.bit_length() + 7) // 8
    
    if isinstance(plaintext, str):
        plaintext = plaintext.encode('utf-8')
    
    # Apply padding
    padded = pkcs1_pad(plaintext, key_size)
    
    # Convert to integer
    m = int.from_bytes(padded, 'big')
    
    # Encrypt: c = m^e mod n
    c = pow(m, e, n)
    
    # Convert back to bytes
    return c.to_bytes(key_size, 'big')

def rsa_decrypt(ciphertext, private_key):
    """Decrypt data using RSA private key."""
    n, d = private_key['n'], private_key['d']
    
    # Convert ciphertext to integer
    if isinstance(ciphertext, bytes):
        c = int.from_bytes(ciphertext, 'big')
    else:
        c = int(ciphertext)
    
    # Decrypt: m = c^d mod n
    m = pow(c, d, n)
    
    # Convert back to bytes
    key_size = (n.bit_length() + 7) // 8
    padded = m.to_bytes(key_size, 'big')
    
    # Remove padding
    return pkcs1_unpad(padded)

def recover_rsa_factors(n, e, d):
    """
    Recover p and q from n, e, and d using the fact that ed ≡ 1 (mod φ(n)).
    Based on the algorithm from "Twenty Years of Attacks on the RSA Cryptosystem".
    """
    # Calculate k = ed - 1
    k = e * d - 1
    
    # k must be even since ed ≡ 1 (mod φ(n)) and φ(n) is even for n > 2
    if k % 2 != 0:
        raise ValueError("Invalid key parameters: ed - 1 is not even")
    
    # Write k = 2^t * r where r is odd
    t = 0
    r = k
    while r % 2 == 0:
        r //= 2
        t += 1
    
    # Try different values of g until we find factors
    for attempt in range(100):  # Try up to 100 random values
        g = secrets.randbelow(n - 2) + 2  # Random integer between 2 and n-1
        
        if gcd(g, n) != 1:
            continue
        
        # Compute y = g^r mod n
        y = pow(g, r, n)
        
        if y == 1 or y == n - 1:
            continue
        
        # Square y repeatedly
        for _ in range(t - 1):
            x = y
            y = pow(y, 2, n)
            
            if y == 1:
                # Found a non-trivial square root of 1
                p = gcd(x - 1, n)
                if 1 < p < n:
                    q = n // p
                    return p, q
            
            if y == n - 1:
                break
    
    raise ValueError("Failed to factor n. The key parameters might be invalid.")

def regenerate_full_keys(n, e, d):
    """
    Regenerate complete RSA key parameters from n, e, and d.
    """
    try:
        # Recover p and q
        p, q = recover_rsa_factors(n, e, d)
        
        # Ensure p > q for consistency
        if p < q:
            p, q = q, p
        
        # Verify the factorization
        if p * q != n:
            raise ValueError("Factor verification failed")
        
        # Calculate phi(n)
        phi = (p - 1) * (q - 1)
        
        # Verify that ed ≡ 1 (mod phi)
        if (e * d) % phi != 1:
            raise ValueError("Key consistency check failed: ed ≢ 1 (mod φ(n))")
        
        # Calculate CRT parameters for optimization
        dp = d % (p - 1)  # d mod (p-1)
        dq = d % (q - 1)  # d mod (q-1)
        qinv = mod_inverse(q, p)  # q^-1 mod p
        
        return {
            'public': {'n': n, 'e': e},
            'private': {
                'n': n, 'd': d, 'p': p, 'q': q, 'phi': phi,
                'dp': dp, 'dq': dq, 'qinv': qinv
            }
        }
        
    except Exception as e:
        raise ValueError(f"Key regeneration failed: {e}")

def save_full_key_to_file(key_data, filename):
    """Save complete key data to file including all parameters."""
    with open(filename, 'w') as f:
        if 'e' in key_data:  # Public key
            f.write(f"# RSA Public Key\n")
            f.write(f"n = {hex(key_data['n'])}\n")
            f.write(f"e = {hex(key_data['e'])}\n")
        else:  # Private key with all parameters
            f.write(f"# RSA Private Key (Complete)\n")
            f.write(f"n = {hex(key_data['n'])}\n")
            f.write(f"d = {hex(key_data['d'])}\n")
            f.write(f"p = {hex(key_data['p'])}\n")
            f.write(f"q = {hex(key_data['q'])}\n")
            f.write(f"phi = {hex(key_data['phi'])}\n")
            if 'dp' in key_data:
                f.write(f"dp = {hex(key_data['dp'])}\n")
                f.write(f"dq = {hex(key_data['dq'])}\n")
                f.write(f"qinv = {hex(key_data['qinv'])}\n")

def load_key_from_file(filename):
    """Load key data from file."""
    key_data = {}
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('#') or not line:
                continue
            if ' = ' in line:
                key, value = line.split(' = ', 1)
                key_data[key.strip()] = int(value, 16)
    return key_data

def main():
    print("RSA Encryption/Decryption Tool")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. Generate new RSA key pair")
        print("2. Encrypt data")
        print("3. Decrypt data")
        print("4. Load existing keys")
        print("5. Manual key input (use extracted d and n)")
        print("6. Regenerate full keys from n, e, d")
        print("7. Exit")
        
        choice = input("\nChoose option (1-7): ").strip()
        
        if choice == '1':
            bits = int(input("Key size in bits (default 1024): ") or "1024")
            keys = generate_rsa_keypair(bits)
            
            print(f"\nGenerated {bits}-bit RSA key pair!")
            print(f"Public key (n): {hex(keys['public']['n'])}")
            print(f"Public key (e): {hex(keys['public']['e'])}")
            print(f"Private key (d): {hex(keys['private']['d'])}")
            
            # Save keys
            save_full_key_to_file(keys['public'], 'rsa_public.key')
            save_full_key_to_file(keys['private'], 'rsa_private.key')
            print("\nKeys saved to rsa_public.key and rsa_private.key")
            
        elif choice == '2':
            # Encrypt
            try:
                public_key = load_key_from_file('rsa_public.key')
                plaintext = input("Enter text to encrypt: ")
                
                ciphertext = rsa_encrypt(plaintext, public_key)
                
                print(f"\nCiphertext (hex): {ciphertext.hex()}")
                print(f"Ciphertext (base64): {base64.b64encode(ciphertext).decode()}")
                
                # Save to file
                with open('encrypted.bin', 'wb') as f:
                    f.write(ciphertext)
                print("Ciphertext saved to encrypted.bin")
                
            except FileNotFoundError:
                print("Public key file not found. Generate keys first.")
            except Exception as e:
                print(f"Encryption failed: {e}")
                
        elif choice == '3':
            # Decrypt
            try:
                private_key = load_key_from_file('rsa_private.key')
                
                print("Ciphertext input options:")
                print("1. From file (encrypted.bin)")
                print("2. Enter hex directly")
                print("3. Enter base64 directly")
                
                ct_choice = input("Choose option (1-3): ").strip()
                
                if ct_choice == '1':
                    with open('encrypted.bin', 'rb') as f:
                        ciphertext = f.read()
                elif ct_choice == '2':
                    hex_input = input("Enter hex ciphertext: ").strip()
                    ciphertext = bytes.fromhex(hex_input)
                elif ct_choice == '3':
                    b64_input = input("Enter base64 ciphertext: ").strip()
                    ciphertext = base64.b64decode(b64_input)
                else:
                    print("Invalid choice")
                    continue
                
                plaintext = rsa_decrypt(ciphertext, private_key)
                
                print(f"\nPlaintext (hex): {plaintext.hex()}")
                print(f"Plaintext (text): {plaintext.decode('utf-8', errors='ignore')}")
                
                # Check for flag pattern
                text = plaintext.decode('utf-8', errors='ignore')
                if 'flag{' in text.lower() or 'ctf{' in text.lower():
                    print(f"\n🚩 FLAG FOUND: {text}")
                
            except FileNotFoundError as e:
                print(f"File not found: {e}")
            except Exception as e:
                print(f"Decryption failed: {e}")
                
        elif choice == '4':
            # Load and display keys
            try:
                if os.path.exists('rsa_public.key'):
                    pub = load_key_from_file('rsa_public.key')
                    print(f"Public key loaded - n: {hex(pub['n'])}, e: {hex(pub['e'])}")
                
                if os.path.exists('rsa_private.key'):
                    priv = load_key_from_file('rsa_private.key')
                    print(f"Private key loaded - d: {hex(priv['d'])}")
                    
            except Exception as e:
                print(f"Error loading keys: {e}")
                
        elif choice == '5':
            # Manual key input
            print("\nManual Key Input")
            print("Enter your extracted RSA parameters:")
            
            try:
                # Get modulus (n)
                n_input = input("Enter modulus (n) [hex or decimal]: ").strip()
                if n_input.startswith('0x') or n_input.startswith('0X'):
                    n = int(n_input, 16)
                elif all(c in '0123456789abcdefABCDEF' for c in n_input):
                    n = int(n_input, 16)
                else:
                    n = int(n_input)
                
                # Get private exponent (d)
                d_input = input("Enter private exponent (d) [hex or decimal]: ").strip()
                if d_input.startswith('0x') or d_input.startswith('0X'):
                    d = int(d_input, 16)
                elif all(c in '0123456789abcdefABCDEF' for c in d_input):
                    d = int(d_input, 16)
                else:
                    d = int(d_input)
                
                # Get public exponent (e) - default to 65537
                e_input = input("Enter public exponent (e) [default 65537]: ").strip()
                if e_input:
                    if e_input.startswith('0x') or e_input.startswith('0X'):
                        e = int(e_input, 16)
                    elif all(c in '0123456789abcdefABCDEF' for c in e_input):
                        e = int(e_input, 16)
                    else:
                        e = int(e_input)
                else:
                    e = 65537
                
                # Create key objects
                public_key = {'n': n, 'e': e}
                private_key = {'n': n, 'd': d}
                
                print(f"\nKey parameters loaded:")
                print(f"Modulus (n): {hex(n)} ({n.bit_length()} bits)")
                print(f"Public exponent (e): {e}")
                print(f"Private exponent (d): {hex(d)}")
                
                # Save to temporary files
                save_full_key_to_file(public_key, 'temp_public.key')
                save_full_key_to_file(private_key, 'temp_private.key')
                print("Keys saved to temp_public.key and temp_private.key")
                
                # Test encryption/decryption
                while True:
                    print("\nTest operations:")
                    print("1. Test encrypt with public key")
                    print("2. Test decrypt with private key")
                    print("3. Encrypt then decrypt (full test)")
                    print("4. Back to main menu")
                    
                    test_choice = input("Choose test (1-4): ").strip()
                    
                    if test_choice == '1':
                        # Test encryption
                        plaintext = input("Enter text to encrypt: ")
                        try:
                            ciphertext = rsa_encrypt(plaintext, public_key)
                            print(f"Encrypted (hex): {ciphertext.hex()}")
                            print(f"Encrypted (base64): {base64.b64encode(ciphertext).decode()}")
                        except Exception as e:
                            print(f"Encryption failed: {e}")
                    
                    elif test_choice == '2':
                        # Test decryption
                        print("Enter ciphertext to decrypt:")
                        print("1. Hex format")
                        print("2. Base64 format")
                        fmt_choice = input("Choose format (1-2): ").strip()
                        
                        try:
                            if fmt_choice == '1':
                                hex_input = input("Enter hex ciphertext: ").strip()
                                ciphertext = bytes.fromhex(hex_input)
                            else:
                                b64_input = input("Enter base64 ciphertext: ").strip()
                                ciphertext = base64.b64decode(b64_input)
                            
                            plaintext = rsa_decrypt(ciphertext, private_key)
                            text = plaintext.decode('utf-8', errors='ignore')
                            print(f"Decrypted: {text}")
                            
                            if 'flag{' in text.lower() or 'ctf{' in text.lower():
                                print(f"\n🚩 FLAG FOUND: {text}")
                                
                        except Exception as e:
                            print(f"Decryption failed: {e}")
                    
                    elif test_choice == '3':
                        # Full test
                        test_message = input("Enter test message: ")
                        try:
                            # Encrypt
                            ciphertext = rsa_encrypt(test_message, public_key)
                            print(f"Encrypted: {ciphertext.hex()}")
                            
                            # Decrypt
                            decrypted = rsa_decrypt(ciphertext, private_key)
                            decrypted_text = decrypted.decode('utf-8')
                            print(f"Decrypted: {decrypted_text}")
                            
                            if test_message == decrypted_text:
                                print("✅ Test passed! Encryption/decryption working correctly.")
                            else:
                                print("❌ Test failed! Decrypted text doesn't match original.")
                                
                        except Exception as e:
                            print(f"Test failed: {e}")
                    
                    elif test_choice == '4':
                        break
                    else:
                        print("Invalid choice")
                
            except ValueError as e:
                print(f"Invalid input: {e}")
            except Exception as e:
                print(f"Error: {e}")
                
        elif choice == '6':
            # Regenerate full keys from n, e, d
            print("\nRegenerate Full RSA Keys")
            print("This will recover p, q, and φ(n) from n, e, and d")
            
            try:
                # Get the three main parameters
                n_input = input("Enter modulus (n) [hex or decimal]: ").strip()
                if n_input.startswith('0x') or n_input.startswith('0X'):
                    n = int(n_input, 16)
                elif all(c in '0123456789abcdefABCDEF' for c in n_input):
                    n = int(n_input, 16)
                else:
                    n = int(n_input)
                
                e_input = input("Enter public exponent (e) [hex or decimal]: ").strip()
                if e_input.startswith('0x') or e_input.startswith('0X'):
                    e = int(e_input, 16)
                elif all(c in '0123456789abcdefABCDEF' for c in e_input):
                    e = int(e_input, 16)
                else:
                    e = int(e_input)
                
                d_input = input("Enter private exponent (d) [hex or decimal]: ").strip()
                if d_input.startswith('0x') or d_input.startswith('0X'):
                    d = int(d_input, 16)
                elif all(c in '0123456789abcdefABCDEF' for c in d_input):
                    d = int(d_input, 16)
                else:
                    d = int(d_input)
                
                print("\nRegenerating full key parameters...")
                print("This may take a moment for large keys...")
                
                # Regenerate full keys
                full_keys = regenerate_full_keys(n, e, d)
                
                print("\n✅ Key regeneration successful!")
                print("=" * 50)
                print("COMPLETE RSA KEY PARAMETERS")
                print("=" * 50)
                
                pub = full_keys['public']
                priv = full_keys['private']
                
                print(f"Modulus (n):          {hex(pub['n'])}")
                print(f"Public exponent (e):  {pub['e']}")
                print(f"Private exponent (d): {hex(priv['d'])}")
                print(f"Prime p:              {hex(priv['p'])}")
                print(f"Prime q:              {hex(priv['q'])}")
                print(f"Euler's totient φ(n): {hex(priv['phi'])}")
                print(f"CRT exponent dp:      {hex(priv['dp'])}")
                print(f"CRT exponent dq:      {hex(priv['dq'])}")
                print(f"CRT coefficient qinv: {hex(priv['qinv'])}")
                
                print(f"\nKey verification:")
                print(f"p × q = n: {'✅' if priv['p'] * priv['q'] == pub['n'] else '❌'}")
                print(f"ed ≡ 1 (mod φ): {'✅' if (pub['e'] * priv['d']) % priv['phi'] == 1 else '❌'}")
                print(f"Key size: {pub['n'].bit_length()} bits")
                
                # Save complete keys
                save_full_key_to_file(full_keys['public'], 'regenerated_public.key')
                save_full_key_to_file(full_keys['private'], 'regenerated_private.key')
                print("\nComplete keys saved to regenerated_public.key and regenerated_private.key")
                
                # Offer to test the regenerated keys
                test_regen = input("\nTest the regenerated keys? (y/n): ").strip().lower()
                if test_regen == 'y':
                    test_message = "Hello, this is a test message for the regenerated RSA keys!"
                    try:
                        # Test encryption/decryption
                        ciphertext = rsa_encrypt(test_message, full_keys['public'])
                        decrypted = rsa_decrypt(ciphertext, full_keys['private'])
                        decrypted_text = decrypted.decode('utf-8')
                        
                        print(f"\nTest message: {test_message}")
                        print(f"Encrypted: {ciphertext.hex()[:64]}...")
                        print(f"Decrypted: {decrypted_text}")
                        
                        if test_message == decrypted_text:
                            print("✅ Regenerated keys work perfectly!")
                        else:
                            print("❌ Key test failed!")
                            
                    except Exception as e:
                        print(f"❌ Key test failed: {e}")
                
            except ValueError as e:
                print(f"❌ Error: {e}")
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                
        elif choice == '7':
            break
        else:
            print("Invalid choice")

if __name__ == "__main__":
    main()
