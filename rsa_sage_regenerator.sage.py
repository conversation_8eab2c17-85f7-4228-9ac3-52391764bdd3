#!/usr/bin/env sage
"""
RSA Key Regenerator using SageMath
Parses RSA key parameters from brut.txt and regenerates complete RSA keys using Sage
"""


# This file was *autogenerated* from the file rsa_sage_regenerator.sage
from sage.all_cmdline import *   # import sage library

_sage_const_1 = Integer(1); _sage_const_2 = Integer(2); _sage_const_0 = Integer(0); _sage_const_1000 = Integer(1000); _sage_const_100 = Integer(100); _sage_const_64 = Integer(64); _sage_const_50 = Integer(50); _sage_const_30 = Integer(30)
import os
import sys
from Crypto.Cipher import PKCS1_OAEP
from Crypto.PublicKey import RSA
from Crypto.Util.number import bytes_to_long, long_to_bytes

def parse_brut_file(filename="brut.txt"):
    """
    Parse the brut.txt file to extract RSA key parameters.
    """
    if not os.path.exists(filename):
        raise FileNotFoundError(f"File {filename} not found")
    
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    # Clean lines and remove empty ones
    lines = [line.strip() for line in lines if line.strip()]
    
    # Find the sections
    pexp_start = None
    mod_start = None
    e_start = None
    
    for i, line in enumerate(lines):
        if line == 'pexp':
            pexp_start = i + _sage_const_1 
        elif line == 'mod':
            mod_start = i + _sage_const_1 
        elif line == 'e':
            e_start = i + _sage_const_1 
    
    if pexp_start is None:
        raise ValueError("Could not find 'pexp' section in file")
    if mod_start is None:
        raise ValueError("Could not find 'mod' section in file")
    if e_start is None:
        raise ValueError("Could not find 'e' section in file")
    
    # Extract private exponent (d)
    d_str = ""
    for i in range(pexp_start, len(lines)):
        if lines[i] in ['mod', 'e']:
            break
        d_str += lines[i]
    
    # Extract modulus (n)
    n_str = ""
    for i in range(mod_start, len(lines)):
        if lines[i] in ['pexp', 'e']:
            break
        n_str += lines[i]
    
    # Extract public exponent (e)
    e_str = ""
    for i in range(e_start, len(lines)):
        if lines[i] in ['pexp', 'mod']:
            break
        e_str += lines[i]
    
    # Convert to integers
    try:
        d = Integer(d_str)
        n = Integer(n_str)
        e = Integer(e_str)
    except ValueError as ve:
        raise ValueError(f"Failed to parse integers from file: {ve}")
    
    return n, e, d

def recover_rsa_factors_sage(n, e, d):
    """
    Recover p and q from n, e, and d using SageMath's powerful factorization.
    """
    print("Using Sage to recover RSA factors...")
    
    # Method 1: Direct factorization using Sage (if feasible)
    print("Attempting direct factorization...")
    try:
        factors = factor(n)
        if len(factors) == _sage_const_2 :
            p, q = factors[_sage_const_0 ][_sage_const_0 ], factors[_sage_const_1 ][_sage_const_0 ]
            if p * q == n:
                return p, q
    except:
        print("Direct factorization failed or took too long")
    
    # Method 2: Use the mathematical relationship ed ≡ 1 (mod φ(n))
    print("Using ed ≡ 1 (mod φ(n)) relationship...")
    
    # Calculate k = ed - 1
    k = e * d - _sage_const_1 
    
    # k must be even since φ(n) is even for n > 2
    if k % _sage_const_2  != _sage_const_0 :
        raise ValueError("Invalid key parameters: ed - 1 is not even")
    
    # Write k = 2^t * r where r is odd
    t = _sage_const_0 
    r = k
    while r % _sage_const_2  == _sage_const_0 :
        r //= _sage_const_2 
        t += _sage_const_1 
    
    print(f"k = ed - 1 = 2^{t} * {r}")
    
    # Try different values of g until we find factors
    for attempt in range(_sage_const_1000 ):  # Try up to 1000 random values
        g = randint(_sage_const_2 , n-_sage_const_1 )
        
        if gcd(g, n) != _sage_const_1 :
            continue
        
        # Compute y = g^r mod n
        y = power_mod(g, r, n)
        
        if y == _sage_const_1  or y == n - _sage_const_1 :
            continue
        
        # Square y repeatedly
        for i in range(t - _sage_const_1 ):
            x = y
            y = power_mod(y, _sage_const_2 , n)
            
            if y == _sage_const_1 :
                # Found a non-trivial square root of 1
                p = gcd(x - _sage_const_1 , n)
                if _sage_const_1  < p < n:
                    q = n // p
                    return p, q
            
            if y == n - _sage_const_1 :
                break
        
        if attempt % _sage_const_100  == _sage_const_0 :
            print(f"Tried {attempt + _sage_const_1 } attempts...")
    
    raise ValueError("Failed to factor n using probabilistic method")

def create_pem_keys(n, e, d, p, q):
    """
    Create PEM format RSA keys using the recovered parameters.
    """
    # Calculate additional CRT parameters
    dp = d % (p - _sage_const_1 )
    dq = d % (q - _sage_const_1 )
    qinv = inverse_mod(q, p)
    
    # Create RSA key object using PyCrypto
    key_params = (int(n), int(e), int(d), int(p), int(q))
    rsa_key = RSA.construct(key_params)
    
    # Export keys
    private_pem = rsa_key.export_key('PEM')
    public_pem = rsa_key.publickey().export_key('PEM')
    
    return private_pem, public_pem, rsa_key

def test_keys_with_file(rsa_key, test_filename="ciphtest.txt"):
    """
    Test the regenerated keys by encrypting and decrypting a test file.
    """
    if not os.path.exists(test_filename):
        print(f"Warning: Test file {test_filename} not found. Using default test message.")
        test_content = "This is a test message for RSA key verification."
    else:
        with open(test_filename, 'r') as f:
            test_content = f.read().strip()
    
    print(f"Testing with content: '{test_content}'")
    
    try:
        # Create cipher objects
        public_key = rsa_key.publickey()
        cipher_encrypt = PKCS1_OAEP.new(public_key)
        cipher_decrypt = PKCS1_OAEP.new(rsa_key)
        
        # Encrypt the test content
        print("Encrypting test content...")
        test_bytes = test_content.encode('utf-8')
        ciphertext = cipher_encrypt.encrypt(test_bytes)
        print(f"Encryption successful. Ciphertext length: {len(ciphertext)} bytes")
        print(f"Ciphertext (first 64 hex chars): {ciphertext.hex()[:_sage_const_64 ]}...")
        
        # Decrypt the ciphertext
        print("Decrypting ciphertext...")
        decrypted_bytes = cipher_decrypt.decrypt(ciphertext)
        decrypted_text = decrypted_bytes.decode('utf-8')
        print(f"Decryption successful. Decrypted: '{decrypted_text}'")
        
        # Verify round-trip
        if test_content == decrypted_text:
            print("✅ Round-trip test PASSED! Keys are working correctly.")
            return True
        else:
            print("❌ Round-trip test FAILED! Decrypted text doesn't match original.")
            print(f"Original:  '{test_content}'")
            print(f"Decrypted: '{decrypted_text}'")
            return False
            
    except Exception as e:
        print(f"❌ Key test failed with error: {e}")
        return False

def main():
    print("RSA Key Regenerator using SageMath")
    print("=" * _sage_const_50 )
    
    try:
        # Parse the brut.txt file
        print("Parsing brut.txt file...")
        n, e, d = parse_brut_file()
        
        print("Successfully extracted RSA parameters:")
        print(f"Modulus (n):          {n.nbits()} bits")
        print(f"Public exponent (e):  {e}")
        print(f"Private exponent (d): {d.nbits()} bits")
        print()
        
        # Verify basic key relationship
        print("Verifying key relationship...")
        phi_multiple = (e * d - _sage_const_1 )
        print(f"ed - 1 = {phi_multiple}")
        print(f"ed - 1 is even: {'✅' if phi_multiple % _sage_const_2  == _sage_const_0  else '❌'}")
        
        if phi_multiple % _sage_const_2  != _sage_const_0 :
            print("❌ Invalid key parameters: ed - 1 should be even")
            return
        
        # Recover p and q using Sage
        print("\nRecovering prime factors...")
        p, q = recover_rsa_factors_sage(n, e, d)
        
        # Ensure p > q for consistency
        if p < q:
            p, q = q, p
        
        print(f"✅ Successfully recovered prime factors!")
        print(f"Prime p: {p} ({p.nbits()} bits)")
        print(f"Prime q: {q} ({q.nbits()} bits)")
        print()
        
        # Verify the factorization
        if p * q != n:
            raise ValueError("Factor verification failed: p * q ≠ n")
        
        # Calculate and verify phi(n)
        phi = (p - _sage_const_1 ) * (q - _sage_const_1 )
        if (e * d) % phi != _sage_const_1 :
            raise ValueError("Key consistency check failed: ed ≢ 1 (mod φ(n))")
        
        print("Key verification:")
        print(f"p × q = n: ✅")
        print(f"ed ≡ 1 (mod φ): ✅")
        print(f"φ(n) = {phi}")
        print()
        
        # Create PEM format keys
        print("Creating PEM format keys...")
        private_pem, public_pem, rsa_key = create_pem_keys(n, e, d, p, q)
        
        # Save the keys
        with open('regenerated_private.pem', 'wb') as f:
            f.write(private_pem)
        with open('regenerated_public.pem', 'wb') as f:
            f.write(public_pem)
        
        print("Keys saved to regenerated_private.pem and regenerated_public.pem")
        print()
        
        # Display key information
        print("RSA Key Information:")
        print("-" * _sage_const_30 )
        print(f"Key size: {n.nbits()} bits")
        print(f"Modulus (n): {hex(int(n))}")
        print(f"Public exponent (e): {e}")
        print(f"Private exponent (d): {hex(int(d))}")
        print(f"Prime p: {hex(int(p))}")
        print(f"Prime q: {hex(int(q))}")
        print()
        
        # Test the keys with ciphtest.txt
        print("Testing regenerated keys with ciphtest.txt...")
        print("-" * _sage_const_50 )
        test_success = test_keys_with_file(rsa_key)
        
        if test_success:
            print("\n🎉 SUCCESS! RSA keys have been successfully regenerated and tested.")
            print("You can now use the regenerated_private.pem and regenerated_public.pem files.")
        else:
            print("\n⚠️  Keys were regenerated but testing failed. Please check the implementation.")
            
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        print("Make sure brut.txt exists in the current directory.")
    except ValueError as e:
        print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

