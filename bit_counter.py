#!/usr/bin/env python3
import base64
import os

def count_bits_in_file(filename):
    """
    Base64 decode the contents of a file and count the total number of bits.
    """
    try:
        # Read the file content
        with open(filename, 'r') as file:
            content = file.read().strip()
        
        # Base64 decode the content
        try:
            # Fix padding if needed
            missing_padding = len(content) % 4
            if missing_padding:
                content += '=' * (4 - missing_padding)
            
            decoded_bytes = base64.b64decode(content)
        except Exception as e:
            print(f"Error decoding base64: {e}")
            return None
        
        # Count bits (each byte = 8 bits)
        total_bits = len(decoded_bytes) * 8
        
        print(f"File: {filename}")
        print(f"Original content length: {len(content)} characters")
        print(f"Decoded bytes length: {len(decoded_bytes)} bytes")
        print(f"Total bits: {total_bits} bits")
        
        # Show decoded content (if printable)
        try:
            decoded_text = decoded_bytes.decode('utf-8')
            print(f"Decoded content: {decoded_text}")
        except UnicodeDecodeError:
            print(f"Decoded content (hex): {decoded_bytes.hex()}")
        
        return total_bits
        
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found.")
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def main():
    filename = "brut.txt"
    
    # Check if file exists in current directory
    if not os.path.exists(filename):
        print(f"'{filename}' not found in current directory.")
        print("Please make sure the file exists and contains base64 encoded data.")
        return
    
    bit_count = count_bits_in_file(filename)
    
    if bit_count is not None:
        print(f"\n🔢 Total bit count: {bit_count}")

if __name__ == "__main__":
    main()
