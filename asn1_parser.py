#!/usr/bin/env python3
import base64
import os
import struct

def parse_asn1_data(data):
    """
    Basic ASN.1 parser using built-in Python capabilities.
    """
    try:
        print("ASN.1 Structure (Basic Parser):")
        print("-" * 40)
        
        pos = 0
        parse_asn1_tlv(data, pos, 0)
        
        return True
        
    except Exception as e:
        print(f"Error parsing ASN.1: {e}")
        return None

def parse_asn1_tlv(data, pos, indent):
    """
    Parse ASN.1 TLV (Tag-Length-Value) structure.
    """
    if pos >= len(data):
        return pos
        
    spaces = "  " * indent
    
    # Parse tag
    tag = data[pos]
    pos += 1
    
    # Parse length
    length_byte = data[pos]
    pos += 1
    
    if length_byte & 0x80 == 0:
        # Short form
        length = length_byte
    else:
        # Long form
        length_bytes = length_byte & 0x7F
        if length_bytes == 0:
            print(f"{spaces}Indefinite length form not supported")
            return pos
        
        length = 0
        for i in range(length_bytes):
            length = (length << 8) | data[pos]
            pos += 1
    
    # Determine tag class and type
    tag_class = (tag & 0xC0) >> 6
    constructed = (tag & 0x20) != 0
    tag_number = tag & 0x1F
    
    tag_classes = ["Universal", "Application", "Context", "Private"]
    
    print(f"{spaces}Tag: 0x{tag:02x} ({tag_classes[tag_class]}, {'Constructed' if constructed else 'Primitive'}, {tag_number})")
    print(f"{spaces}Length: {length}")
    
    if constructed and length > 0:
        # Parse nested structure
        end_pos = pos + length
        while pos < end_pos:
            pos = parse_asn1_tlv(data, pos, indent + 1)
    else:
        # Show value
        if length <= 32:  # Show small values
            value = data[pos:pos+length]
            if all(32 <= b <= 126 for b in value):  # Printable ASCII
                print(f"{spaces}Value: '{value.decode('ascii')}'")
            else:
                print(f"{spaces}Value: {value.hex()}")
        else:
            print(f"{spaces}Value: {data[pos:pos+16].hex()}... ({length} bytes)")
        
        pos += length
    
    return pos

def analyze_file_with_asn1(filename):
    """
    Decode base64 file content and analyze as ASN.1.
    """
    try:
        # Read the file content
        with open(filename, 'r') as file:
            content = file.read().strip()
        
        print(f"File: {filename}")
        print(f"Original content length: {len(content)} characters")
        
        # Try different approaches to decode the content
        decoded_bytes = None
        
        # First, try base64 decoding with different padding strategies
        for padding_strategy in [0, 1, 2, 3]:
            try:
                test_content = content + ('=' * padding_strategy)
                decoded_bytes = base64.b64decode(test_content, validate=True)
                print(f"Successfully decoded with {padding_strategy} padding chars")
                break
            except Exception:
                continue
        
        # If base64 decoding failed, try treating as raw bytes
        if decoded_bytes is None:
            try:
                # Remove whitespace and newlines first
                clean_content = ''.join(content.split())
                decoded_bytes = base64.b64decode(clean_content, validate=True)
                print("Successfully decoded after removing whitespace")
            except Exception:
                # Try treating as hex
                try:
                    clean_content = ''.join(content.split())
                    if all(c in '0123456789abcdefABCDEF' for c in clean_content):
                        decoded_bytes = bytes.fromhex(clean_content)
                        print("Treating content as hex data")
                    else:
                        # Last resort: treat as raw UTF-8 bytes
                        decoded_bytes = content.encode('utf-8')
                        print("Treating content as raw UTF-8 data")
                except Exception as e:
                    print(f"All decoding methods failed: {e}")
                    return None
        
        print(f"Decoded bytes length: {len(decoded_bytes)} bytes")
        print(f"Total bits: {len(decoded_bytes) * 8} bits")
        
        print(f"\nHex dump of first 64 bytes:")
        print(decoded_bytes[:64].hex())
        if len(decoded_bytes) > 64:
            print("...")
        
        print(f"\n" + "="*50)
        print("ASN.1 ANALYSIS")
        print("="*50)
        
        # Try to parse as ASN.1
        asn1_obj = parse_asn1_data(decoded_bytes)
        
        return asn1_obj
        
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found.")
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def main():
    filename = "brut.txt"
    
    # Check if file exists in current directory
    if not os.path.exists(filename):
        print(f"'{filename}' not found in current directory.")
        print("Please make sure the file exists and contains base64 encoded ASN.1 data.")
        return
    
    analyze_file_with_asn1(filename)

if __name__ == "__main__":
    main()
