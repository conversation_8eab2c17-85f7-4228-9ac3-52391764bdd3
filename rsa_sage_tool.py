#!/usr/bin/env sage
"""
RSA Tool with Sage integration for robust factorization and key recovery.
Requires SageMath to be installed.
"""

import base64
import os
import sys

# Check if we're running in Sage
try:
    # These will be available in Sage
    Integer
    factor
    inverse_mod
    gcd
    random_prime
    Mod
except NameError:
    print("ERROR: This script                    # Check for flag patterns
                    flag_patterns = ['flag{', 'FLAG{', 'ctf{', 'CTF{', 'pico{', 'PICO{']
                    for pattern in flag_patterns:quires SageMath to run.")
    print("Please install SageMath or run with: sage rsa_sage_tool.py")
    sys.exit(1)

def parse_number(s):
    """Parse a number from hex or decimal string."""
    s = s.strip()
    if s.startswith('0x') or s.startswith('0X'):
        return Integer(s, 16)
    elif all(c in '0123456789abcdefABCDEF' for c in s) and len(s) > 10:
        # Likely hex without 0x prefix
        try:
            return Integer(s, 16)
        except:
            return Integer(s, 10)
    else:
        return Integer(s, 10)

def sage_factor_n_from_ed(n, e, d):
    """
    Factor n using <PERSON>'s powerful mathematical functions.
    Uses the relationship ed ≡ 1 (mod φ(n)) to recover p and q.
    """
    print(f"Attempting to factor n using Sage...")
    print(f"n = {n}")
    print(f"e = {e}")
    print(f"d = {d}")
    
    # Method 1: Try direct factorization first (works for small n)
    try:
        print("\n[Method 1] Trying direct factorization...")
        factors = factor(n)
        print(f"Direct factorization result: {factors}")
        
        factor_list = list(factors)
        if len(factor_list) == 2:
            p, q = factor_list[0][0], factor_list[1][0]
            if factor_list[0][1] == 1 and factor_list[1][1] == 1:
                print(f"✓ Found factors: p = {p}, q = {q}")
                return p, q
    except Exception as e:
        print(f"Direct factorization failed: {e}")
    
    # Method 2: Use the ed - 1 approach with Sage's arithmetic
    try:
        print("\n[Method 2] Using ed - 1 factorization method...")
        
        # k = ed - 1 = λ(n) * m for some integer m
        k = e * d - 1
        print(f"k = ed - 1 = {k}")
        
        # Write k = 2^s * t where t is odd
        s = 0
        t = k
        while t % 2 == 0:
            t = t // 2
            s += 1
        
        print(f"k = 2^{s} * {t}")
        
        # Try different random bases
        for attempt in range(50):
            # Use Sage's random number generation
            g = ZZ.random_element(2, n-1)
            
            if gcd(g, n) != 1:
                continue
            
            # Compute y = g^t mod n
            y = Mod(g, n)^t
            
            if y == 1 or y == n - 1:
                continue
            
            # Square y repeatedly
            for i in range(s - 1):
                x = y
                y = y^2
                
                if y == 1:
                    # Found non-trivial square root of 1
                    p = gcd(Integer(x) - 1, n)
                    if 1 < p < n:
                        q = n // p
                        print(f"✓ Found factors: p = {p}, q = {q}")
                        return p, q
                
                if y == n - 1:
                    break
        
        print("Method 2 failed to find factors")
        
    except Exception as e:
        print(f"Method 2 failed: {e}")
    
    # Method 3: Try Pollard's rho algorithm with Sage
    try:
        print("\n[Method 3] Trying Pollard's rho algorithm...")
        
        def pollard_rho_sage(n):
            if n % 2 == 0:
                return 2
            
            x = ZZ.random_element(2, n-1)
            y = x
            d = 1
            
            f = lambda x: (x^2 + 1) % n
            
            while d == 1:
                x = f(x)
                y = f(f(y))
                d = gcd(abs(x - y), n)
                
                if d == n:
                    return None
            
            return d if d != 1 else None
        
        factor_found = pollard_rho_sage(n)
        if factor_found and 1 < factor_found < n:
            p = factor_found
            q = n // p
            print(f"✓ Pollard's rho found factors: p = {p}, q = {q}")
            return p, q
        else:
            print("Pollard's rho didn't find factors")
            
    except Exception as e:
        print(f"Method 3 failed: {e}")
    
    # Method 4: Continued fraction attack (if d is small)
    try:
        print("\n[Method 4] Trying continued fraction attack...")
        
        # Convert e/n to continued fraction
        cf = continued_fraction(e/n)
        convergents = cf.convergents()
        
        for conv in convergents[:20]:  # Try first 20 convergents
            k = conv.numerator()
            d_guess = conv.denominator()
            
            if k == 0:
                continue
                
            # Check if this gives us valid d
            phi_guess = (e * d_guess - 1) // k
            
            # Solve x^2 - (n - phi_guess + 1)x + n = 0
            discriminant = (n - phi_guess + 1)^2 - 4*n
            
            if discriminant >= 0:
                sqrt_disc = Integer(discriminant).sqrt(extend=False)
                if sqrt_disc^2 == discriminant:
                    p = ((n - phi_guess + 1) + sqrt_disc) // 2
                    q = ((n - phi_guess + 1) - sqrt_disc) // 2
                    
                    if p * q == n and p > 1 and q > 1:
                        print(f"✓ Continued fraction attack found factors: p = {p}, q = {q}")
                        return p, q
        
        print("Continued fraction attack failed")
        
    except Exception as e:
        print(f"Method 4 failed: {e}")
    
    raise ValueError("All factorization methods failed. The modulus might be too large or the parameters invalid.")

def regenerate_full_rsa_key(n, e, d):
    """
    Regenerate complete RSA key parameters using Sage.
    """
    try:
        # Convert inputs to Sage integers
        n = Integer(n)
        e = Integer(e)
        d = Integer(d)
        
        print(f"Input parameters:")
        print(f"n = {n}")
        print(f"e = {e}")
        print(f"d = {d}")
        print(f"n bit length: {n.nbits()}")
        
        # Verify ed ≡ 1 (mod something)
        product = e * d
        print(f"ed = {product}")
        print(f"ed - 1 = {product - 1}")
        
        # Factor n to get p and q
        p, q = sage_factor_n_from_ed(n, e, d)
        
        # Ensure p > q for consistency
        if p < q:
            p, q = q, p
        
        # Calculate phi(n)
        phi_n = (p - 1) * (q - 1)
        
        # Verify the parameters
        print(f"\n=== Verification ===")
        print(f"p = {p}")
        print(f"q = {q}")
        print(f"p * q = {p * q}")
        print(f"n = {n}")
        print(f"p * q == n: {p * q == n}")
        
        print(f"φ(n) = (p-1)(q-1) = {phi_n}")
        print(f"gcd(e, φ(n)) = {gcd(e, phi_n)}")
        print(f"ed mod φ(n) = {(e * d) % phi_n}")
        
        # Calculate CRT parameters
        dp = d % (p - 1)
        dq = d % (q - 1)
        qinv = inverse_mod(q, p)
        
        # Verify CRT parameters
        print(f"\n=== CRT Parameters ===")
        print(f"dp = d mod (p-1) = {dp}")
        print(f"dq = d mod (q-1) = {dq}")
        print(f"qinv = q^-1 mod p = {qinv}")
        print(f"Verification: q * qinv mod p = {(q * qinv) % p}")
        
        # Test encryption/decryption
        print(f"\n=== Testing ===")
        test_msg = 42
        ciphertext = Mod(test_msg, n)^e
        decrypted = Mod(ciphertext, n)^d
        print(f"Test message: {test_msg}")
        print(f"Encrypted: {ciphertext}")
        print(f"Decrypted: {decrypted}")
        print(f"Test passed: {decrypted == test_msg}")
        
        return {
            'n': n,
            'e': e,
            'd': d,
            'p': p,
            'q': q,
            'phi_n': phi_n,
            'dp': dp,
            'dq': dq,
            'qinv': qinv
        }
        
    except Exception as e:
        print(f"Error in key regeneration: {e}")
        raise

def rsa_decrypt_sage(ciphertext, n, d):
    """Decrypt using Sage's modular arithmetic."""
    if isinstance(ciphertext, str):
        # Try to parse as hex or base64
        try:
            if ciphertext.startswith('0x'):
                c = Integer(ciphertext, 16)
            elif all(c in '0123456789abcdefABCDEF' for c in ciphertext):
                c = Integer(ciphertext, 16)
            else:
                # Try base64
                decoded = base64.b64decode(ciphertext)
                c = Integer(int.from_bytes(decoded, 'big'))
        except:
            raise ValueError("Invalid ciphertext format")
    else:
        c = Integer(ciphertext)
    
    # Decrypt: m = c^d mod n
    m = Mod(c, n)^d
    
    return Integer(m)

def main():
    """Main interactive menu."""
    print("=== RSA Sage Tool ===")
    print("Powered by SageMath for robust mathematical operations")
    print()
    
    while True:
        print("\nOptions:")
        print("1. Regenerate RSA key from n, e, d")
        print("2. Decrypt ciphertext")
        print("3. Factor a number")
        print("4. Exit")
        
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == '1':
            print("\n=== RSA Key Regeneration ===")
            try:
                n_str = input("Enter modulus n (hex or decimal): ").strip()
                e_str = input("Enter public exponent e (default 65537): ").strip()
                d_str = input("Enter private exponent d (hex or decimal): ").strip()
                
                n = parse_number(n_str)
                e = parse_number(e_str) if e_str else 65537
                d = parse_number(d_str)
                
                print("\nRegenerating RSA key parameters...")
                key_params = regenerate_full_rsa_key(n, e, d)
                
                print("\n=== Complete RSA Key Parameters ===")
                for param, value in key_params.items():
                    print(f"{param}: {value}")
                
                # Save to file
                save = input("\nSave key parameters to file? (y/n): ").strip().lower()
                if save == 'y':
                    filename = input("Enter filename (default: recovered_key.txt): ").strip()
                    if not filename:
                        filename = "recovered_key.txt"
                    
                    with open(filename, 'w') as f:
                        f.write("=== Recovered RSA Key Parameters ===\n")
                        for param, value in key_params.items():
                            f.write(f"{param}: {value}\n")
                    
                    print(f"Key parameters saved to {filename}")
                
            except Exception as e:
                print(f"Error: {e}")
        
        elif choice == '2':
            print("\n=== RSA Decryption ===")
            try:
                n_str = input("Enter modulus n (hex or decimal): ").strip()
                d_str = input("Enter private exponent d (hex or decimal): ").strip()
                c_str = input("Enter ciphertext (hex or base64): ").strip()
                
                n = parse_number(n_str)
                d = parse_number(d_str)
                
                decrypted = rsa_decrypt_sage(c_str, n, d)
                
                print(f"\nDecrypted (decimal): {decrypted}")
                print(f"Decrypted (hex): {hex(decrypted)}")
                
                # Try to convert to text
                try:
                    # Convert to bytes
                    hex_str = hex(decrypted)[2:]
                    if len(hex_str) % 2:
                        hex_str = '0' + hex_str
                    
                    decoded_bytes = bytes.fromhex(hex_str)
                    text = decoded_bytes.decode('utf-8', errors='ignore')
                    print(f"Decrypted (text): {text}")
                    
                    # Check for flag patterns
                    flag_patterns = ['flag{', 'FLAG{', 'ctf{', 'CTF{', 'pico{', 'PICO{'}
                    for pattern in flag_patterns:
                        if pattern.lower() in text.lower():
                            print(f"🚩 POTENTIAL FLAG DETECTED: {text}")
                            break
                    
                except Exception as e:
                    print(f"Could not decode as text: {e}")
                
            except Exception as e:
                print(f"Error: {e}")
        
        elif choice == '3':
            print("\n=== Factor a Number ===")
            try:
                n_str = input("Enter number to factor (hex or decimal): ").strip()
                n = parse_number(n_str)
                
                print(f"Factoring {n}...")
                factors = factor(n)
                print(f"Factors: {factors}")
                
            except Exception as e:
                print(f"Error: {e}")
        
        elif choice == '4':
            print("Goodbye!")
            break
        
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
