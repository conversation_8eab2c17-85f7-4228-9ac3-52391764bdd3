#!/usr/bin/env python3
import base64
import os

def mod_inverse(a, m):
    """
    Calculate modular inverse of a mod m using Extended Euclidean Algorithm.
    """
    if m == 1:
        return 0
    
    # Extended Euclidean Algorithm
    def extended_gcd(a, b):
        if a == 0:
            return b, 0, 1
        gcd, x1, y1 = extended_gcd(b % a, a)
        x = y1 - (b // a) * x1
        y = x1
        return gcd, x, y
    
    gcd, x, _ = extended_gcd(a % m, m)
    if gcd != 1:
        raise ValueError("Modular inverse does not exist")
    return (x % m + m) % m

def rsa_decrypt_with_exponent(ciphertext, d, n):
    """
    Decrypt RSA ciphertext using private exponent d and modulus n.
    """
    # Convert ciphertext to integer if it's bytes
    if isinstance(ciphertext, bytes):
        c = int.from_bytes(ciphertext, 'big')
    elif isinstance(ciphertext, str):
        # Try to interpret as hex
        try:
            c = int(ciphertext, 16)
        except ValueError:
            # Try base64 decode then convert
            try:
                ciphertext_bytes = base64.b64decode(ciphertext)
                c = int.from_bytes(ciphertext_bytes, 'big')
            except:
                raise ValueError("Cannot interpret ciphertext format")
    else:
        c = int(ciphertext)
    
    # RSA decryption: m = c^d mod n
    m = pow(c, d, n)
    
    # Convert back to bytes
    # Calculate number of bytes needed
    byte_length = (n.bit_length() + 7) // 8
    
    try:
        plaintext = m.to_bytes(byte_length, 'big')
        # Remove PKCS#1 padding if present
        if plaintext[0:2] == b'\x00\x02':
            # Find the end of padding (0x00 separator)
            separator_index = plaintext.find(b'\x00', 2)
            if separator_index != -1:
                plaintext = plaintext[separator_index + 1:]
        elif plaintext[0:2] == b'\x00\x01':
            # PKCS#1 v1.5 signature padding
            separator_index = plaintext.find(b'\x00', 2)
            if separator_index != -1:
                plaintext = plaintext[separator_index + 1:]
        
        return plaintext
    except OverflowError:
        # If too large for bytes, return as hex
        return hex(m)

def main():
    print("RSA Decryption Script")
    print("=" * 40)
    
    # Get private exponent
    d_input = input("Enter private exponent (d) [hex or decimal]: ").strip()
    try:
        if d_input.startswith('0x') or d_input.startswith('0X'):
            d = int(d_input, 16)
        elif all(c in '0123456789abcdefABCDEF' for c in d_input):
            d = int(d_input, 16)
        else:
            d = int(d_input)
    except ValueError:
        print("Invalid private exponent format")
        return
    
    # Get modulus
    n_input = input("Enter modulus (n) [hex or decimal]: ").strip()
    try:
        if n_input.startswith('0x') or n_input.startswith('0X'):
            n = int(n_input, 16)
        elif all(c in '0123456789abcdefABCDEF' for c in n_input):
            n = int(n_input, 16)
        else:
            n = int(n_input)
    except ValueError:
        print("Invalid modulus format")
        return
    
    print(f"\nPrivate exponent (d): {d}")
    print(f"Modulus (n): {n}")
    print(f"Key size: {n.bit_length()} bits")
    
    # Get ciphertext
    print("\nCiphertext input options:")
    print("1. File path")
    print("2. Direct input (hex/base64)")
    
    choice = input("Choose option (1 or 2): ").strip()
    
    if choice == '1':
        filepath = input("Enter ciphertext file path: ").strip()
        try:
            with open(filepath, 'rb') as f:
                ciphertext = f.read()
        except FileNotFoundError:
            print(f"File not found: {filepath}")
            return
    else:
        ciphertext = input("Enter ciphertext (hex or base64): ").strip()
    
    # Decrypt
    try:
        plaintext = rsa_decrypt_with_exponent(ciphertext, d, n)
        
        print("\n" + "=" * 40)
        print("DECRYPTION RESULT")
        print("=" * 40)
        
        if isinstance(plaintext, bytes):
            print(f"Plaintext (hex): {plaintext.hex()}")
            
            # Try to decode as text
            try:
                text = plaintext.decode('utf-8')
                print(f"Plaintext (text): {text}")
            except UnicodeDecodeError:
                try:
                    text = plaintext.decode('ascii', errors='ignore')
                    print(f"Plaintext (ASCII): {text}")
                except:
                    print("Plaintext is not valid text")
            
            # Look for flag patterns
            text_lower = plaintext.decode('utf-8', errors='ignore').lower()
            if 'flag' in text_lower or 'ctf' in text_lower:
                print(f"\n🚩 Possible flag found: {plaintext.decode('utf-8', errors='ignore')}")
        else:
            print(f"Plaintext: {plaintext}")
            
    except Exception as e:
        print(f"Decryption failed: {e}")

if __name__ == "__main__":
    main()
