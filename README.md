# OCR Character Validation System

## 🎯 Clean and Simple OCR Setup

This workspace contains a clean, focused OCR system for character-by-character validation.

## 📁 Files

### Core Scripts:
- **`true_character_ocr.py`** - Main character-by-character OCR validation system
- **`simple_manual_ocr.py`** - Simple line-by-line validation (backup option)

### Input Files:
- **`key.png`** - Image to be processed
- **`rsa_private_key.pem`** - Reference file for comparison

### Environment:
- **`ocr_env/`** - Python virtual environment with OCR dependencies

## 🚀 Usage

### 1. Activate Environment
```bash
source ocr_env/bin/activate
```

### 2. Run Character-by-Character OCR
```bash
# Basic usage - validate first 4 lines
python true_character_ocr.py key.png --reference rsa_private_key.pem --output validated_key.txt --lines 1

# Validate more lines
python true_character_ocr.py key.png --reference rsa_private_key.pem --output validated_key.txt --lines 4
```

## 🎮 Interactive Commands

During character validation:
- **ENTER** - Accept the current character
- **Type character** - Correct the character (e.g., type 'l' to replace '1')
- **'skip'** - Skip this character
- **'quit'** - Finish validation early
- **'auto'** - Auto-accept remaining alphanumeric characters
- **'newline'** or **'\\n'** - Insert a newline character

## 📷 Visual Feedback

- **`char.png`** - Shows current character with neighboring context
  - ✅ **Context display** - Shows 5 neighboring characters on each side (11 total)
  - ✅ **Character highlighting** - Current character highlighted with red border
  - ✅ **Enhanced sizing** - Wider dimensions (600x420 pixels, ~15KB) for context
  - ✅ **Character info** - ASCII value, type, position, and extended context
  - ✅ **Real-time updates** - Automatically updates for each character
  - ✅ **Visual emphasis** - Red and yellow borders for clear identification
  - ✅ **Auto cleanup** - Removed automatically when validation completes

## 🎯 Features

- **Character-level precision** - Validate each character individually
- **Visual feedback** - See exactly what character you're validating
- **Reference comparison** - Compare against known good text
- **Auto-mode** - Speed up validation for high-confidence characters
- **Progress tracking** - See your position and context
- **Error correction** - Fix mistakes as you go
- **Configurable positioning** - Easily adjust constants for fine-tuning

## 🔧 Customizing Positioning

To fine-tune character positioning, edit the constants at the top of `true_character_ocr.py`:

### **Character Positioning**
```python
CHAR_WIDTH_RATIO = 0.75          # Character spacing (0.70 = closer, 0.80 = further)
LEFT_MARGIN_RATIO = 0.005        # Starting position (0.001 = closer to edge)
ADDITIONAL_LEFT_SHIFT = 8        # Extra left shift in pixels
LINE_HEIGHT_RATIO = 0.85         # Line spacing (0.80 = closer lines)
```

### **Context Display**
```python
CONTEXT_CHARS = 5                # Neighbors on each side (3-7 recommended)
CONTEXT_SCALE = 6                # Image zoom level (4-8 recommended)
MAX_SCALED_WIDTH = 800           # Maximum image width
```

### **Visual Highlighting**
```python
HIGHLIGHT_BORDER_WIDTH = 4       # Red border thickness
HIGHLIGHT_ACCENT_WIDTH = 2       # Yellow accent thickness
```

**After editing:** Save the file and test with `python true_character_ocr.py key.png --lines 1`

## 📊 Success Metrics

The system aims for 95%+ similarity with reference text through:
- Individual character validation
- Visual confirmation of each character
- Context-aware corrections
- Real-time progress feedback

## 🧹 Clean Workspace

This workspace has been cleaned up to contain only essential files:
- Removed duplicate/experimental scripts
- Removed excessive documentation
- Kept only the working OCR system
- Maintained clean file structure

## ✅ Fixed Issues

### **char.png Single Character Display**
- **Problem**: Previously showed entire text lines instead of individual characters
- **Solution**: Improved character position estimation and region extraction
- **Result**: Now shows only the single character being validated
- **Verification**: Tested with proper dimensions (500x400px vs previous 600x21680px)

### **Character Extraction Accuracy**
- **Enhanced position estimation** for monospace fonts (RSA keys)
- **Constrained region extraction** to prevent oversized character regions
- **Improved visual feedback** with better scaling and highlighting
- **Reliable single character focus** throughout validation process

### **Fine-Tuned Character Positioning**
- **Perfect starting position**: First character now starts at 0px (was ~20px too far right)
- **Optimal advancement rate**: 11px per character (was ~25px too fast)
- **Minimal left margin**: 0.5% of image width (was 2%, then 5%)
- **Enhanced left shift**: 8 pixels + additional -8px adjustment (was 3px)
- **Improved character width**: 75% of line width (was 90%, then 85%)
- **Better line spacing**: 85% of calculated height (prevents going too far down)

### **Enhanced Context Display**
- **Wider extraction region**: Shows 5 neighboring characters on each side (11 total)
- **Current character highlighting**: Red border with yellow accent for clear identification
- **Context-aware sizing**: Wider images (600x420px) to accommodate neighboring characters
- **Visual feedback**: Current character clearly highlighted within its context

### **Configurable Constants**
- **Easy customization**: All positioning parameters defined as constants at top of script
- **Character spacing**: `CHAR_WIDTH_RATIO = 0.75` (adjust character advancement rate)
- **Starting position**: `LEFT_MARGIN_RATIO = 0.005` (adjust left edge positioning)
- **Fine-tuning**: `ADDITIONAL_LEFT_SHIFT = 8` (pixel-level positioning adjustment)
- **Context display**: `CONTEXT_CHARS = 5` (neighbors on each side)
- **Image scaling**: `CONTEXT_SCALE = 6` (zoom level for character display)
