# OCR Positioning Constants Reference

## 🎛️ Quick Reference for Adjusting Character Positioning

Edit these constants at the top of `true_character_ocr.py` to fine-tune positioning:

### 📍 **Character Positioning**

| Constant | Current Value | Purpose | Adjustment Tips |
|----------|---------------|---------|-----------------|
| `CHAR_WIDTH_RATIO` | 0.75 | Character spacing | **Decrease** (0.70) = closer together<br>**Increase** (0.80) = further apart |
| `LEFT_MARGIN_RATIO` | 0.005 | Starting position | **Decrease** (0.001) = start closer to left<br>**Increase** (0.01) = start further right |
| `ADDITIONAL_LEFT_SHIFT` | 8 | Extra left shift (px) | **Increase** (12) = shift more left<br>**Decrease** (4) = shift less left |
| `LINE_HEIGHT_RATIO` | 0.85 | Line spacing | **Decrease** (0.80) = lines closer<br>**Increase** (0.90) = lines further |

### 📷 **Context Display**

| Constant | Current Value | Purpose | Adjustment Tips |
|----------|---------------|---------|-----------------|
| `CONTEXT_CHARS` | 5 | Neighbors shown | **3** = fewer neighbors<br>**7** = more neighbors |
| `CONTEXT_SCALE` | 6 | Image zoom level | **4** = smaller images<br>**8** = larger images |
| `MAX_SCALED_WIDTH` | 800 | Max image width | **600** = narrower<br>**1000** = wider |
| `MIN_SCALED_WIDTH` | 200 | Min image width | Adjust for minimum size |

### 🎨 **Visual Highlighting**

| Constant | Current Value | Purpose | Adjustment Tips |
|----------|---------------|---------|-----------------|
| `HIGHLIGHT_BORDER_WIDTH` | 4 | Red border thickness | **2** = thinner<br>**6** = thicker |
| `HIGHLIGHT_ACCENT_WIDTH` | 2 | Yellow accent thickness | **1** = thinner<br>**3** = thicker |

## 🔧 **Common Adjustments**

### If characters start too far right:
- Decrease `LEFT_MARGIN_RATIO` (try 0.001)
- Increase `ADDITIONAL_LEFT_SHIFT` (try 12)

### If characters advance too fast:
- Decrease `CHAR_WIDTH_RATIO` (try 0.70)

### If characters advance too slow:
- Increase `CHAR_WIDTH_RATIO` (try 0.80)

### If lines are too far apart:
- Decrease `LINE_HEIGHT_RATIO` (try 0.80)

### If you want more context:
- Increase `CONTEXT_CHARS` (try 7)
- Increase `MAX_SCALED_WIDTH` (try 1000)

### If images are too large:
- Decrease `CONTEXT_SCALE` (try 4)
- Decrease `MAX_SCALED_WIDTH` (try 600)

## 🧪 **Testing Changes**

1. Edit constants in `true_character_ocr.py`
2. Save the file
3. Test: `python true_character_ocr.py key.png --lines 1`
4. Check `char.png` to see the changes
5. Adjust and repeat until satisfied

## 📋 **Current Optimal Settings**

The current values have been optimized for RSA key text:
- Perfect left alignment (starts at 0px)
- Proper character advancement (11px per character)
- Good context display (5 neighbors each side)
- Clear visual highlighting

**Only adjust if you need different behavior for your specific use case!**
